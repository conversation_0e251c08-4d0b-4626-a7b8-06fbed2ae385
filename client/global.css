@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 240 100% 99%;
    --foreground: 231 84% 5%;

    --card: 0 0% 100%;
    --card-foreground: 231 84% 5%;

    --popover: 0 0% 100%;
    --popover-foreground: 231 84% 5%;

    --primary: 142 78% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 196 100% 95%;
    --secondary-foreground: 231 84% 5%;

    --muted: 225 25% 95%;
    --muted-foreground: 225 15% 50%;

    --accent: 45 100% 85%;
    --accent-foreground: 45 100% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 225 25% 88%;
    --input: 225 25% 88%;
    --ring: 142 78% 45%;

    --radius: 1rem;

    --beginner: 142 78% 45%;
    --intermediate: 45 100% 60%;
    --advanced: 0 84% 60%;
    --success: 120 100% 40%;
    --warning: 35 100% 55%;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 231 84% 5%;
    --foreground: 240 100% 99%;

    --card: 231 84% 8%;
    --card-foreground: 240 100% 99%;

    --popover: 231 84% 8%;
    --popover-foreground: 240 100% 99%;

    --primary: 142 78% 55%;
    --primary-foreground: 0 0% 0%;

    --secondary: 231 84% 12%;
    --secondary-foreground: 240 100% 99%;

    --muted: 231 84% 12%;
    --muted-foreground: 225 15% 65%;

    --accent: 45 100% 25%;
    --accent-foreground: 45 100% 85%;

    --destructive: 0 84% 65%;
    --destructive-foreground: 0 0% 0%;

    --border: 231 84% 15%;
    --input: 231 84% 15%;
    --ring: 142 78% 55%;

    --beginner: 142 78% 55%;
    --intermediate: 45 100% 65%;
    --advanced: 0 84% 65%;
    --success: 120 100% 50%;
    --warning: 35 100% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
